import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Brain, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  TrendingUp, 
  Shield, 
  FileText, 
  Lightbulb,
  ChevronDown,
  ChevronRight,
  Eye,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  HelpCircle,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AIInsight {
  id: string;
  type: 'risk' | 'opportunity' | 'compliance' | 'recommendation' | 'clause';
  title: string;
  description: string;
  confidence: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  explanation: string;
  evidence: string[];
  suggestions: string[];
  impact: string;
  location?: {
    page?: number;
    section?: string;
    clause?: string;
  };
}

interface InteractiveAIInsightsProps {
  contractId: string;
  insights: AIInsight[];
  onFeedback?: (insightId: string, feedback: 'helpful' | 'not_helpful', comment?: string) => void;
  onExplainMore?: (insightId: string) => void;
  showConfidenceScores?: boolean;
  allowFeedback?: boolean;
}

export const InteractiveAIInsights: React.FC<InteractiveAIInsightsProps> = ({
  contractId,
  insights,
  onFeedback,
  onExplainMore,
  showConfidenceScores = true,
  allowFeedback = true
}) => {
  const [selectedInsight, setSelectedInsight] = useState<string | null>(null);
  const [expandedInsights, setExpandedInsights] = useState<Set<string>>(new Set());
  const [feedbackGiven, setFeedbackGiven] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'severity' | 'confidence' | 'type'>('severity');

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'opportunity':
        return <TrendingUp className="h-5 w-5 text-green-500" />;
      case 'compliance':
        return <Shield className="h-5 w-5 text-blue-500" />;
      case 'recommendation':
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      case 'clause':
        return <FileText className="h-5 w-5 text-purple-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const toggleExpanded = (insightId: string) => {
    const newExpanded = new Set(expandedInsights);
    if (newExpanded.has(insightId)) {
      newExpanded.delete(insightId);
    } else {
      newExpanded.add(insightId);
    }
    setExpandedInsights(newExpanded);
  };

  const handleFeedback = (insightId: string, feedback: 'helpful' | 'not_helpful') => {
    setFeedbackGiven(prev => new Set([...prev, insightId]));
    onFeedback?.(insightId, feedback);
  };

  const filteredInsights = insights.filter(insight => 
    filterType === 'all' || insight.type === filterType
  );

  const sortedInsights = [...filteredInsights].sort((a, b) => {
    switch (sortBy) {
      case 'severity':
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      case 'confidence':
        return b.confidence - a.confidence;
      case 'type':
        return a.type.localeCompare(b.type);
      default:
        return 0;
    }
  });

  const insightCounts = insights.reduce((acc, insight) => {
    acc[insight.type] = (acc[insight.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-6 w-6 text-blue-600" />
              <CardTitle>AI Insights & Analysis</CardTitle>
            </div>
            <Badge variant="outline">
              {insights.length} insights found
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(insightCounts).map(([type, count]) => (
              <div key={type} className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getInsightIcon(type)}
                </div>
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-sm text-gray-600 capitalize">{type}s</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Filters and Controls */}
      <div className="flex items-center justify-between">
        <Tabs value={filterType} onValueChange={setFilterType}>
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="risk">Risks</TabsTrigger>
            <TabsTrigger value="opportunity">Opportunities</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="recommendation">Recommendations</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="text-sm border rounded px-2 py-1"
          >
            <option value="severity">Severity</option>
            <option value="confidence">Confidence</option>
            <option value="type">Type</option>
          </select>
        </div>
      </div>

      {/* Insights List */}
      <div className="space-y-4">
        {sortedInsights.map((insight) => (
          <Card key={insight.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    {getInsightIcon(insight.type)}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-900">{insight.title}</h3>
                        <Badge className={getSeverityColor(insight.severity)}>
                          {insight.severity}
                        </Badge>
                        {showConfidenceScores && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Badge variant="outline" className={getConfidenceColor(insight.confidence)}>
                                  {Math.round(insight.confidence * 100)}% confidence
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>AI confidence in this analysis</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      <p className="text-gray-600">{insight.description}</p>
                      {insight.location && (
                        <div className="text-sm text-gray-500 mt-1">
                          {insight.location.section && `Section: ${insight.location.section}`}
                          {insight.location.page && ` • Page ${insight.location.page}`}
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpanded(insight.id)}
                  >
                    {expandedInsights.has(insight.id) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {/* Expanded Content */}
                <Collapsible open={expandedInsights.has(insight.id)}>
                  <CollapsibleContent className="space-y-4">
                    {/* Explanation */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <HelpCircle className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Why this matters</span>
                      </div>
                      <p className="text-blue-800">{insight.explanation}</p>
                    </div>

                    {/* Evidence */}
                    {insight.evidence.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Evidence found:</h4>
                        <ul className="space-y-1">
                          {insight.evidence.map((evidence, index) => (
                            <li key={index} className="flex items-start space-x-2">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700">{evidence}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Suggestions */}
                    {insight.suggestions.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Recommendations:</h4>
                        <ul className="space-y-1">
                          {insight.suggestions.map((suggestion, index) => (
                            <li key={index} className="flex items-start space-x-2">
                              <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700">{suggestion}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Impact */}
                    {insight.impact && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <TrendingUp className="h-4 w-4 text-yellow-600" />
                          <span className="font-medium text-yellow-900">Potential Impact</span>
                        </div>
                        <p className="text-yellow-800">{insight.impact}</p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onExplainMore?.(insight.id)}
                        >
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Explain More
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View in Document
                        </Button>
                      </div>

                      {/* Feedback */}
                      {allowFeedback && !feedbackGiven.has(insight.id) && (
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">Was this helpful?</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleFeedback(insight.id, 'helpful')}
                          >
                            <ThumbsUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleFeedback(insight.id, 'not_helpful')}
                          >
                            <ThumbsDown className="h-4 w-4" />
                          </Button>
                        </div>
                      )}

                      {feedbackGiven.has(insight.id) && (
                        <div className="text-sm text-green-600">
                          Thank you for your feedback!
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {sortedInsights.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No insights found</h3>
            <p className="text-gray-600">
              {filterType === 'all' 
                ? 'Run an AI analysis to generate insights for this contract.'
                : `No ${filterType} insights found. Try a different filter.`
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
